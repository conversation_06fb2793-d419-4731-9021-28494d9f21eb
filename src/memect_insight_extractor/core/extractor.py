"""主提取器模块"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Tuple

from ..config import ExtractionConfig
from ..models.extraction_request import ExtractionRequest
from ..models.extraction_result import ExtractionResult, ExtractionStatus
from ..utils.logger import configure_logger, get_logger
from .error_handler import ErrorHandler
from .llm_client import LLMClient
from .prompt_builder import PromptBuilder
from .result_parser import ResultParser
from .text_splitter import TextChunk, TextSplitter

logger = get_logger(__name__)


class DocumentExtractor:
    """文档提取器

    协调整个提取流程的主要类
    """

    def __init__(self, config: ExtractionConfig):
        """初始化文档提取器

        Args:
            config: 提取配置
        """
        self.config = config

        # 配置日志
        configure_logger(
            level=config.log_level,
            log_file=config.log_file,
            debug_mode=config.debug_mode
        )

        self.llm_client = LLMClient(config)
        self.prompt_builder = PromptBuilder()
        self.text_splitter = TextSplitter(config)
        self.result_parser = ResultParser(config)
        self.error_handler = ErrorHandler(config)

        if config.debug_mode:
            logger.debug(f"DocumentExtractor初始化完成，配置: {config.model_dump()}")

    async def extract(
        self, text_content: str, schema: Dict[str, Any], **kwargs
    ) -> ExtractionResult:
        """执行信息提取

        Args:
            text_content: 文档文本内容
            schema: JSON Schema定义
            **kwargs: 其他参数

        Returns:
            提取结果
        """
        # 创建提取请求
        request = ExtractionRequest(text_content=text_content, schema=schema, **kwargs)

        return await self.extract_from_request(request)

    async def extract_from_request(
        self, request: ExtractionRequest
    ) -> ExtractionResult:
        """从请求对象执行提取

        Args:
            request: 提取请求

        Returns:
            提取结果
        """
        start_time = time.time()
        result = ExtractionResult(status=ExtractionStatus.SUCCESS)

        try:
            # 计算文档的token数量
            doc_tokens = self.text_splitter.count_tokens(request.text_content)
            logger.info(f"开始提取任务，文档长度: {doc_tokens} tokens")

            # DEBUG模式下输出详细的请求信息
            if self.config.debug_mode:
                logger.debug(f"提取请求详情: document_id={request.document_id}, "
                           f"extraction_purpose_id={request.extraction_purpose_id}, "
                           f"config_version_id={request.config_version_id}")
                logger.debug(f"Schema字段数: {len(request.schema.get('properties', {}))}")

            # 1. 检查是否需要分段
            if self.text_splitter.should_split(request.text_content):
                logger.debug("文档需要分段处理")
                extracted_data = await self._extract_with_splitting(request, result)
            else:
                logger.debug("文档使用单块处理")
                extracted_data = await self._extract_single_chunk(request, result)

            # 2. 设置提取结果
            if extracted_data is not None:
                result.extracted_data = extracted_data
                if not result.has_errors():
                    result.status = ExtractionStatus.SUCCESS
                else:
                    result.status = ExtractionStatus.PARTIAL
            else:
                result.status = ExtractionStatus.FAILED

            # 3. 记录处理时间
            result.processing_time = time.time() - start_time
            result.metadata.update(
                {
                    "text_tokens": doc_tokens,
                    "config_version_id": request.config_version_id,
                    "document_id": request.document_id,
                    "extraction_purpose_id": request.extraction_purpose_id,
                }
            )

            logger.info(
                f"提取任务完成，状态: {result.status}, 耗时: {result.processing_time:.2f}秒"
            )

            # DEBUG模式下输出详细的结果信息
            if self.config.debug_mode:
                logger.debug(f"提取结果元数据: {result.metadata}")
                if result.extracted_data is not None:
                    if isinstance(result.extracted_data, dict):
                        logger.debug(f"提取的数据字段: {list(result.extracted_data.keys())}")
                    elif isinstance(result.extracted_data, list):
                        logger.debug(f"提取的数据类型: 数组，长度: {len(result.extracted_data)}")
                    else:
                        logger.debug(f"提取的数据类型: {type(result.extracted_data).__name__}，值: {result.extracted_data}")
                if result.errors:
                    logger.debug(f"错误数量: {len(result.errors)}")
                    for error in result.errors:
                        logger.debug(f"错误详情: {error.error_type} - {error.error_message}")

        except Exception as e:
            logger.error(f"提取任务异常: {str(e)}")
            self.error_handler.handle_llm_error(e, result)
            result.status = ExtractionStatus.FAILED
            result.processing_time = time.time() - start_time

        return result

    async def _extract_single_chunk(
        self, request: ExtractionRequest, result: ExtractionResult
    ) -> Optional[Any]:
        """提取单个文本块

        Args:
            request: 提取请求
            result: 提取结果对象

        Returns:
            提取的数据或None
        """
        logger.debug("执行单块提取")

        try:
            # 构建提示词
            prompt = self.prompt_builder.build_prompt(request)

            # 调用LLM
            async with self.llm_client as client:
                raw_response = await self.error_handler.execute_with_retry(
                    client.extract_text, prompt
                )

            # 解析和校验结果
            extracted_data, validation_errors = self.result_parser.parse_and_validate(
                raw_response, request.schema
            )

            if validation_errors:
                self.error_handler.handle_validation_errors(validation_errors, result)

                # 尝试自我修正
                if extracted_data is None and self._should_try_correction(
                    validation_errors
                ):
                    extracted_data = await self._attempt_self_correction(
                        request, raw_response, validation_errors[0], result
                    )

            return extracted_data

        except Exception as e:
            self.error_handler.handle_llm_error(e, result)
            return None

    async def _extract_with_splitting(
        self, request: ExtractionRequest, result: ExtractionResult
    ) -> Optional[Any]:
        """分段提取

        Args:
            request: 提取请求
            result: 提取结果对象

        Returns:
            合并后的提取数据或None
        """
        logger.debug("执行分段提取")

        # 分割文本
        chunks = self.text_splitter.split_text(request.text_content)
        result.metadata["total_chunks"] = len(chunks)

        # 并发处理配置
        if self.config.enable_parallel:
            chunk_results = await self._process_chunks_parallel(request, chunks, result)
        else:
            chunk_results = await self._process_chunks_sequential(
                request, chunks, result
            )

        # 合并结果
        if chunk_results:
            merged_data = self.text_splitter.merge_results(chunk_results)
            result.metadata["successful_chunks"] = len(chunk_results)
            return merged_data

        return None

    async def _process_chunks_parallel(
        self,
        request: ExtractionRequest,
        chunks: List[TextChunk],
        result: ExtractionResult,
    ) -> List[Tuple[int, Any]]:
        """并行处理文本块

        Args:
            request: 提取请求
            chunks: 文本块列表
            result: 提取结果对象

        Returns:
            成功的块结果列表
        """
        logger.debug(f"并行处理 {len(chunks)} 个文本块")

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.max_parallel_chunks)

        async def process_chunk(
            chunk: TextChunk,
        ) -> Optional[Tuple[int, Any]]:
            async with semaphore:
                return await self._process_single_chunk(request, chunk, result)

        # 并发执行
        tasks = [process_chunk(chunk) for chunk in chunks]
        chunk_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤成功的结果
        successful_results = []
        for i, chunk_result in enumerate(chunk_results):
            if isinstance(chunk_result, Exception):
                logger.error(f"块 {i} 处理异常: {str(chunk_result)}")
                self.error_handler.handle_llm_error(chunk_result, result, i)
            elif chunk_result is not None:
                successful_results.append(chunk_result)

        return successful_results

    async def _process_chunks_sequential(
        self,
        request: ExtractionRequest,
        chunks: List[TextChunk],
        result: ExtractionResult,
    ) -> List[Tuple[int, Any]]:
        """顺序处理文本块

        Args:
            request: 提取请求
            chunks: 文本块列表
            result: 提取结果对象

        Returns:
            成功的块结果列表
        """
        logger.debug(f"顺序处理 {len(chunks)} 个文本块")

        successful_results = []

        for chunk in chunks:
            chunk_result = await self._process_single_chunk(request, chunk, result)
            if chunk_result is not None:
                successful_results.append(chunk_result)

        return successful_results

    async def _process_single_chunk(
        self, request: ExtractionRequest, chunk: TextChunk, result: ExtractionResult
    ) -> Optional[Tuple[int, Any]]:
        """处理单个文本块

        Args:
            request: 提取请求
            chunk: 文本块
            result: 提取结果对象

        Returns:
            (块索引, 提取数据) 或 None
        """
        try:
            chunk_tokens = chunk.token_count or self.text_splitter.count_tokens(chunk.content)
            logger.debug(f"处理块 {chunk.chunk_index}，长度: {chunk_tokens} tokens")

            # 构建提示词
            prompt = self.prompt_builder.build_prompt(request, chunk.content)

            # 调用LLM
            async with self.llm_client as client:
                raw_response = await self.error_handler.execute_with_retry(
                    client.extract_text, prompt
                )

            # 解析和校验结果
            extracted_data, validation_errors = self.result_parser.parse_and_validate(
                raw_response, request.schema
            )

            if validation_errors:
                self.error_handler.handle_validation_errors(
                    validation_errors, result, chunk.chunk_index
                )

            if extracted_data is not None:
                return (chunk.chunk_index, extracted_data)

        except Exception as e:
            self.error_handler.handle_llm_error(e, result, chunk.chunk_index)

        return None

    async def _attempt_self_correction(
        self,
        request: ExtractionRequest,
        failed_response: str,
        error_message: str,
        result: ExtractionResult,
    ) -> Optional[Dict[str, Any]]:
        """尝试自我修正

        Args:
            request: 原始请求
            failed_response: 失败的响应
            error_message: 错误消息
            result: 提取结果对象

        Returns:
            修正后的数据或None
        """
        logger.info("尝试自我修正")

        try:
            # 构建修正提示词
            original_prompt = self.prompt_builder.build_prompt(request)
            correction_prompt = self.error_handler.create_self_correction_prompt(
                original_prompt, failed_response, error_message
            )

            # 调用LLM进行修正
            async with self.llm_client as client:
                corrected_response = await client.extract_text(correction_prompt)

            # 解析修正后的结果
            corrected_data, correction_errors = self.result_parser.parse_and_validate(
                corrected_response, request.schema
            )

            if correction_errors:
                logger.warning(f"自我修正仍有错误: {correction_errors}")
                return None

            logger.info("自我修正成功")
            return corrected_data

        except Exception as e:
            logger.error(f"自我修正失败: {str(e)}")
            return None

    def _should_try_correction(self, validation_errors: List[str]) -> bool:
        """判断是否应该尝试自我修正

        Args:
            validation_errors: 校验错误列表

        Returns:
            是否应该尝试修正
        """
        # 如果是JSON解析错误或简单的格式错误，尝试修正
        correctable_patterns = [
            "无法解析为有效的JSON格式",
            "Schema校验失败",
        ]

        for error in validation_errors:
            for pattern in correctable_patterns:
                if pattern in error:
                    return True

        return False

"""结果解析与校验模块"""

import json
import re
from typing import Any, Dict, List, Optional, Tuple

import jsonschema

from ..utils.logger import get_logger
from ..utils.tokenizer_manager import get_tokenizer_manager

logger = get_logger(__name__)


class ResultParser:
    """结果解析与校验器

    负责解析LLM返回的结果并进行Schema校验
    """

    def __init__(self, config=None):
        """初始化结果解析器

        Args:
            config: 提取配置对象
        """
        self.config = config
        self.tokenizer_manager = None
        try:
            self.tokenizer_manager = get_tokenizer_manager()
        except Exception as e:
            logger.warning(f"tokenizer初始化失败: {e}，将使用字符数估算")

    def parse_and_validate(
        self, raw_response: str, schema: Dict[str, Any]
    ) -> Tuple[Optional[Any], List[str]]:
        """解析并校验LLM响应

        Args:
            raw_response: LLM原始响应
            schema: JSON Schema

        Returns:
            (解析结果, 错误列表)
        """
        errors = []
        debug_mode = self.config and self.config.debug_mode

        # 检查空响应
        if not raw_response.strip():
            logger.warning("LLM返回空响应")
            errors.append("LLM返回空响应")
            return None, errors

        # 控制调试输出频率（只输出前几次）
        if debug_mode:
            response_tokens = self._count_tokens(raw_response)
            logger.info(f"LLM原始响应长度: {response_tokens} tokens")
            if response_tokens < 300:  # 只有较短的响应才完整输出（基于token数）
                logger.debug(f"LLM原始响应内容:\n{raw_response}")
            else:
                # 截取前150个token对应的字符数（大约500字符）
                logger.debug(f"LLM原始响应内容（前500字符）:\n{raw_response[:500]}...")

        # 1. 解析JSON
        parsed_data = self._parse_json(raw_response, schema)
        if parsed_data is None:
            error_msg = "无法解析为有效的JSON格式"
            errors.append(error_msg)
            if debug_mode:
                logger.error(f"{error_msg}，原始响应: {raw_response[:200]}...")
            return None, errors

        # 记录解析结果
        if debug_mode:
            logger.debug(f"JSON解析成功，数据类型: {type(parsed_data).__name__}")

        # 2. 处理嵌套结构问题
        normalized_data = self._normalize_structure(parsed_data, schema)

        # 3. 数据清洗（包含null值清理）
        final_data = self._clean_and_normalize(normalized_data, schema)

        # 4. Schema校验
        validation_errors = self._validate_schema(final_data, schema)
        if validation_errors:
            errors.extend(validation_errors)
            if debug_mode:
                logger.warning(f"Schema校验失败: {validation_errors}")

        if debug_mode:
            logger.debug(f"最终清洗后的数据类型: {type(final_data).__name__}")

        return final_data, errors

    def _normalize_structure(self, data: Any, schema: Dict[str, Any]) -> Any:
        """处理LLM返回的嵌套结构问题

        Args:
            data: 解析后的数据
            schema: JSON Schema

        Returns:
            标准化后的数据
        """
        schema_type = schema.get("type", "object")
        debug_mode = self.config and self.config.debug_mode

        # 如果期望的是数组，但LLM返回了包装在对象中的数组
        if schema_type == "array" and isinstance(data, dict):
            if debug_mode:
                logger.debug("检测到期望数组但返回对象，尝试提取数组")

            # 查找对象中的数组字段
            for key, value in data.items():
                if isinstance(value, list):
                    if debug_mode:
                        logger.debug(f"从对象的 '{key}' 字段中提取数组")
                    return value

            # 如果没有找到数组，检查是否所有值都是相同类型的对象
            values = list(data.values())
            if values and all(isinstance(v, dict) for v in values):
                if debug_mode:
                    logger.debug("将对象的值转换为数组")
                return values

        # 如果期望的是对象，但LLM返回了数组
        elif schema_type == "object" and isinstance(data, list):
            if debug_mode:
                logger.debug("检测到期望对象但返回数组，尝试提取对象")

            # 如果数组只有一个元素且是对象，返回该对象
            if len(data) == 1 and isinstance(data[0], dict):
                if debug_mode:
                    logger.debug("从单元素数组中提取对象")
                return data[0]

            # 如果数组中都是对象，尝试合并它们
            if all(isinstance(item, dict) for item in data):
                if debug_mode:
                    logger.debug("尝试合并数组中的对象")
                merged = {}
                for item in data:
                    merged.update(item)
                return merged

        return data



    def _parse_json(self, raw_response: str, schema: Dict[str, Any]) -> Optional[Any]:
        """解析JSON字符串

        Args:
            raw_response: 原始响应字符串
            schema: JSON Schema，用于确定期望的数据类型

        Returns:
            解析后的数据或None
        """
        # 清理响应文本
        cleaned_response = self._clean_response_text(raw_response)

        # 获取期望的数据类型
        expected_type = schema.get("type", "object")

        # 尝试直接解析
        try:
            parsed_data = json.loads(cleaned_response)
            return self._validate_parsed_type(parsed_data, expected_type)
        except json.JSONDecodeError:
            pass

        # 尝试提取JSON块
        json_content = self._extract_json_block(cleaned_response, expected_type)
        if json_content:
            try:
                parsed_data = json.loads(json_content)
                return self._validate_parsed_type(parsed_data, expected_type)
            except json.JSONDecodeError:
                pass

        # 尝试修复常见的JSON错误
        fixed_json = self._fix_common_json_errors(cleaned_response)
        if fixed_json:
            try:
                parsed_data = json.loads(fixed_json)
                return self._validate_parsed_type(parsed_data, expected_type)
            except json.JSONDecodeError:
                pass

        # 对于简单类型，尝试直接解析
        if expected_type in ["string", "number", "integer", "boolean"]:
            simple_result = self._parse_simple_type(cleaned_response, expected_type)
            if simple_result is not None:
                return simple_result

        logger.error(f"无法解析JSON: {raw_response[:200]}...")
        return None

    def _clean_response_text(self, text: str) -> str:
        """清理响应文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        # 移除前后空白
        text = text.strip()

        # 移除常见的前缀和后缀
        prefixes_to_remove = [
            "```json",
            "```",
            "JSON:",
            "结果:",
            "提取结果:",
            "答案:",
        ]

        suffixes_to_remove = [
            "```",
            "以上就是提取的结果",
            "这是提取的信息",
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix) :].strip()

        for suffix in suffixes_to_remove:
            if text.endswith(suffix):
                text = text[: -len(suffix)].strip()

        return text

    def _validate_parsed_type(self, parsed_data: Any, expected_type: str) -> Any:
        """验证解析数据的类型是否符合期望

        Args:
            parsed_data: 解析后的数据
            expected_type: 期望的类型

        Returns:
            验证后的数据
        """
        if expected_type == "object" and isinstance(parsed_data, dict):
            return parsed_data
        elif expected_type == "array" and isinstance(parsed_data, list):
            return parsed_data
        elif expected_type == "string" and isinstance(parsed_data, str):
            return parsed_data
        elif expected_type in ["number", "integer"] and isinstance(parsed_data, (int, float)):
            return parsed_data
        elif expected_type == "boolean" and isinstance(parsed_data, bool):
            return parsed_data
        else:
            # 类型不匹配，但仍然返回数据，让后续的Schema校验处理
            return parsed_data

    def _parse_simple_type(self, text: str, expected_type: str) -> Optional[Any]:
        """解析简单类型的数据

        Args:
            text: 文本内容
            expected_type: 期望的类型

        Returns:
            解析后的数据或None
        """
        text = text.strip()

        if expected_type == "string":
            # 移除可能的引号
            if (text.startswith('"') and text.endswith('"')) or \
               (text.startswith("'") and text.endswith("'")):
                return text[1:-1]
            return text

        elif expected_type in ["number", "integer"]:
            try:
                # 移除非数字字符
                cleaned_num = re.sub(r"[^\d.-]", "", text)
                if expected_type == "number":
                    return float(cleaned_num)
                else:
                    return int(float(cleaned_num))
            except ValueError:
                return None

        elif expected_type == "boolean":
            text_lower = text.lower()
            if text_lower in ["true", "yes", "1", "是", "真"]:
                return True
            elif text_lower in ["false", "no", "0", "否", "假"]:
                return False
            return None

        return None

    def _extract_json_block(self, text: str, expected_type: str = "object") -> Optional[str]:
        """从文本中提取JSON块

        Args:
            text: 文本内容
            expected_type: 期望的数据类型

        Returns:
            提取的JSON字符串或None
        """
        # 根据期望类型选择不同的提取策略
        if expected_type == "object":
            # 查找JSON代码块中的对象
            json_block_pattern = r"```(?:json)?\s*(\{.*?\})\s*```"
            match = re.search(json_block_pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()

            # 查找花括号包围的内容
            brace_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(brace_pattern, text, re.DOTALL)
            if matches:
                # 返回最长的匹配
                return max(matches, key=len)

        elif expected_type == "array":
            # 查找JSON代码块中的数组
            json_block_pattern = r"```(?:json)?\s*(\[.*?\])\s*```"
            match = re.search(json_block_pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()

            # 查找方括号包围的内容
            bracket_pattern = r"\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\]"
            matches = re.findall(bracket_pattern, text, re.DOTALL)
            if matches:
                # 返回最长的匹配
                return max(matches, key=len)

        else:
            # 对于简单类型，查找JSON代码块
            json_block_pattern = r"```(?:json)?\s*([^`]+)\s*```"
            match = re.search(json_block_pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return None

    def _fix_common_json_errors(self, text: str) -> Optional[str]:
        """修复常见的JSON错误

        Args:
            text: JSON文本

        Returns:
            修复后的JSON字符串或None
        """
        try:
            # 修复尾随逗号
            text = re.sub(r",\s*}", "}", text)
            text = re.sub(r",\s*]", "]", text)

            # 修复单引号
            text = re.sub(r"'([^']*)':", r'"\1":', text)
            text = re.sub(r":\s*'([^']*)'", r': "\1"', text)

            # 修复未引用的键
            text = re.sub(r"(\w+):", r'"\1":', text)

            return text
        except Exception:
            return None

    def _validate_schema(
        self, data: Any, schema: Dict[str, Any]
    ) -> List[str]:
        """校验数据是否符合Schema

        Args:
            data: 待校验的数据
            schema: JSON Schema

        Returns:
            错误列表
        """
        errors = []

        try:
            jsonschema.validate(data, schema)
        except jsonschema.ValidationError as e:
            errors.append(f"Schema校验失败: {e.message}")
        except jsonschema.SchemaError as e:
            errors.append(f"Schema定义错误: {e.message}")
        except Exception as e:
            errors.append(f"校验过程出错: {str(e)}")

        return errors

    def _clean_and_normalize(
        self, data: Any, schema: Dict[str, Any]
    ) -> Any:
        """清洗和标准化数据，包括移除null值

        Args:
            data: 原始数据
            schema: JSON Schema

        Returns:
            清洗后的数据
        """
        if data is None:
            return None

        schema_type = schema.get("type", "object")

        if schema_type == "object" and isinstance(data, dict):
            cleaned_data = {}
            properties = schema.get("properties", {})

            for key, value in data.items():
                # 跳过null值
                if value is None:
                    continue

                if key in properties:
                    field_schema = properties[key]
                    # 递归清洗嵌套结构
                    if isinstance(value, (dict, list)):
                        cleaned_value = self._clean_and_normalize(value, field_schema)
                        if cleaned_value is not None:  # 只保留非null的清洗结果
                            cleaned_data[key] = cleaned_value
                    else:
                        cleaned_value = self._clean_field_value(value, field_schema)
                        if cleaned_value is not None:
                            cleaned_data[key] = cleaned_value
                else:
                    # 对于schema中未定义的字段，直接保留（如果不是null）
                    cleaned_data[key] = value

            return cleaned_data

        elif schema_type == "array" and isinstance(data, list):
            items_schema = schema.get("items", {})
            cleaned_items = []

            for item in data:
                # 跳过null项
                if item is None:
                    continue

                if items_schema:
                    if items_schema.get("type") == "object" and isinstance(item, dict):
                        # 对于对象类型的数组项，递归清洗
                        cleaned_item = self._clean_and_normalize(item, items_schema)
                        if cleaned_item is not None:
                            cleaned_items.append(cleaned_item)
                    else:
                        # 对于简单类型的数组项，直接清洗
                        cleaned_item = self._clean_field_value(item, items_schema)
                        if cleaned_item is not None:
                            cleaned_items.append(cleaned_item)
                else:
                    # 没有items schema，直接保留非null项
                    cleaned_items.append(item)

            return cleaned_items

        else:
            # 对于简单类型，直接使用字段清洗逻辑
            return self._clean_field_value(data, schema)

    def _clean_field_value(self, value: Any, field_schema: Dict[str, Any]) -> Any:
        """清洗字段值

        Args:
            value: 字段值
            field_schema: 字段Schema

        Returns:
            清洗后的值
        """
        field_type = field_schema.get("type")
        field_format = field_schema.get("format")

        if value is None:
            return None

        # 字符串类型清洗
        if field_type == "string":
            if not isinstance(value, str):
                value = str(value)

            # 清理空白字符
            value = value.strip()

            # 格式特定清洗
            if field_format == "date":
                value = self._clean_date_string(value)
            elif field_format == "email":
                value = self._clean_email_string(value)
            elif field_format == "phone":
                value = self._clean_phone_string(value)

        # 数字类型清洗
        elif field_type in ["number", "integer"]:
            if isinstance(value, str):
                # 移除非数字字符
                cleaned_num = re.sub(r"[^\d.-]", "", value)
                try:
                    value = (
                        float(cleaned_num)
                        if field_type == "number"
                        else int(float(cleaned_num))
                    )
                except ValueError:
                    value = None

        return value

    def _clean_date_string(self, date_str: str) -> str:
        """清洗日期字符串

        Args:
            date_str: 日期字符串

        Returns:
            清洗后的日期字符串
        """
        # 移除常见的日期分隔符周围的空格
        date_str = re.sub(r"\s*[-/年月日]\s*", lambda m: m.group(0).strip(), date_str)
        return date_str

    def _clean_email_string(self, email_str: str) -> str:
        """清洗邮箱字符串

        Args:
            email_str: 邮箱字符串

        Returns:
            清洗后的邮箱字符串
        """
        return email_str.lower().strip()

    def _clean_phone_string(self, phone_str: str) -> str:
        """清洗电话号码字符串

        Args:
            phone_str: 电话号码字符串

        Returns:
            清洗后的电话号码字符串
        """
        # 移除常见的分隔符和空格
        phone_str = re.sub(r"[-\s()]", "", phone_str)
        return phone_str

    def _count_tokens(self, text: str) -> int:
        """计算文本的token数量

        Args:
            text: 文本内容

        Returns:
            token数量
        """
        if self.tokenizer_manager is None:
            # 回退到字符数估算
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

        try:
            # 使用配置中的tokenizer类型和模型，如果没有配置则使用默认值
            tokenizer_type = "openai"
            model_name = "gpt-4"
            if self.config:
                tokenizer_type = getattr(self.config, 'tokenizer_type', 'openai')
                model_name = getattr(self.config, 'llm_model', 'gpt-4')

            return self.tokenizer_manager.count_tokens(text, tokenizer_type, model_name)
        except Exception as e:
            logger.warning(f"token计数失败: {e}，使用字符数估算")
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

"""LLM客户端模块"""

import asyncio
import json
from typing import Any, Dict, List, Optional

import aiohttp

from ..config import ExtractionConfig
from ..utils.logger import get_logger
from ..utils.tokenizer_manager import get_tokenizer_manager

logger = get_logger(__name__)


class LLMClient:
    """LLM API客户端

    负责与大语言模型API进行通信
    """

    def __init__(self, config: ExtractionConfig):
        """初始化LLM客户端

        Args:
            config: 提取配置
        """
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.tokenizer_manager = None
        try:
            self.tokenizer_manager = get_tokenizer_manager()
        except Exception as e:
            logger.warning(f"tokenizer初始化失败: {e}，将使用字符数估算")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def _ensure_session(self) -> None:
        """确保会话已创建"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.config.llm_timeout)
            headers = {
                "Authorization": f"Bearer {self.config.llm_api_key}",
                "Content-Type": "application/json",
                **self.config.extra_headers,
            }
            self.session = aiohttp.ClientSession(timeout=timeout, headers=headers)

    async def close(self) -> None:
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def chat_completion(
        self, messages: List[Dict[str, str]], **kwargs
    ) -> Dict[str, Any]:
        """调用聊天完成API

        Args:
            messages: 消息列表
            **kwargs: 其他参数

        Returns:
            API响应结果

        Raises:
            Exception: API调用失败
        """
        await self._ensure_session()

        # 构建请求数据
        data = {
            "model": self.config.llm_model,
            "messages": messages,
            "temperature": self.config.llm_temperature,
            **kwargs,
        }

        if self.config.llm_max_tokens:
            data["max_tokens"] = self.config.llm_max_tokens

        url = f"{self.config.llm_base_url.rstrip('/')}/chat/completions"

        logger.debug(f"调用LLM API: {url}")

        # DEBUG模式下输出详细的请求信息
        if self.config.debug_mode:
            logger.debug(f"完整请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            logger.debug(f"请求头: {dict(self.session.headers)}")
        else:
            # 非DEBUG模式下只输出关键信息
            logger.debug(f"模型: {data['model']}, 消息数: {len(data['messages'])}")

        try:
            async with self.session.post(url, json=data) as response:
                response_data = await response.json()

                if response.status != 200:
                    error_msg = f"LLM API调用失败: {response.status}"
                    if "error" in response_data:
                        error_msg += f" - {response_data['error']}"
                    logger.error(error_msg)

                    # DEBUG模式下输出完整的错误响应
                    if self.config.debug_mode:
                        logger.debug(f"错误响应详情: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

                    raise Exception(error_msg)

                # DEBUG模式下输出完整响应，否则只输出关键信息
                if self.config.debug_mode:
                    logger.debug(f"完整LLM API响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                else:
                    if "usage" in response_data:
                        usage = response_data["usage"]
                        logger.debug(f"API调用成功，token使用: {usage}")
                    else:
                        logger.debug("API调用成功")

                return response_data

        except aiohttp.ClientError as e:
            error_msg = f"LLM API网络错误: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"LLM API响应解析错误: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def extract_text(self, prompt: str) -> str:
        """提取文本内容

        Args:
            prompt: 提示词

        Returns:
            提取的文本内容
        """
        # DEBUG模式下记录输入提示词
        if self.config.debug_mode:
            prompt_tokens = self._count_tokens(prompt)
            logger.debug(f"输入提示词长度: {prompt_tokens} tokens")
            logger.debug(f"输入提示词内容:\n{prompt}")

        messages = [{"role": "user", "content": prompt}]

        response = await self.chat_completion(messages)

        if "choices" not in response or not response["choices"]:
            raise Exception("LLM API返回格式错误: 缺少choices字段")

        choice = response["choices"][0]
        if "message" not in choice or "content" not in choice["message"]:
            raise Exception("LLM API返回格式错误: 缺少message.content字段")

        result_content = choice["message"]["content"].strip()

        # DEBUG模式下记录输出内容
        if self.config.debug_mode:
            result_tokens = self._count_tokens(result_content)
            logger.debug(f"LLM输出内容长度: {result_tokens} tokens")
            logger.debug(f"LLM输出内容:\n{result_content}")

        return result_content

    def _count_tokens(self, text: str) -> int:
        """计算文本的token数量

        Args:
            text: 文本内容

        Returns:
            token数量
        """
        if self.tokenizer_manager is None:
            # 回退到字符数估算
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

        try:
            # 使用配置中的tokenizer类型和模型
            return self.tokenizer_manager.count_tokens(
                text,
                self.config.tokenizer_type,
                self.config.llm_model
            )
        except Exception as e:
            logger.warning(f"token计数失败: {e}，使用字符数估算")
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

"""错误代码定义"""

from enum import Enum
from typing import Dict, Optional


class ErrorCode(Enum):
    """错误代码枚举"""
    
    # LLM相关错误 (1000-1999)
    LLM_TIMEOUT = "E1001"
    LLM_RATE_LIMIT = "E1002"
    LLM_AUTH_ERROR = "E1003"
    LLM_CONNECTION_ERROR = "E1004"
    LLM_API_ERROR = "E1005"
    LLM_EMPTY_RESPONSE = "E1006"
    LLM_INVALID_RESPONSE = "E1007"
    
    # 解析相关错误 (2000-2999)
    PARSE_JSON_ERROR = "E2001"
    PARSE_SCHEMA_ERROR = "E2002"
    PARSE_VALIDATION_ERROR = "E2003"
    PARSE_TYPE_MISMATCH = "E2004"
    
    # 数据处理错误 (3000-3999)
    DATA_EMPTY_INPUT = "E3001"
    DATA_INVALID_FORMAT = "E3002"
    DATA_MISSING_REQUIRED = "E3003"
    DATA_STRUCTURE_ERROR = "E3004"
    
    # 配置错误 (4000-4999)
    CONFIG_INVALID_SCHEMA = "E4001"
    CONFIG_MISSING_REQUIRED = "E4002"
    CONFIG_INVALID_VALUE = "E4003"
    
    # 系统错误 (5000-5999)
    SYSTEM_UNKNOWN_ERROR = "E5001"
    SYSTEM_RESOURCE_ERROR = "E5002"


class ErrorInfo:
    """错误信息类"""
    
    def __init__(self, code: ErrorCode, message: str, suggestion: Optional[str] = None):
        self.code = code
        self.message = message
        self.suggestion = suggestion
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        result = {
            "code": self.code.value,
            "message": self.message
        }
        if self.suggestion:
            result["suggestion"] = self.suggestion
        return result


# 错误信息映射
ERROR_INFO_MAP: Dict[ErrorCode, ErrorInfo] = {
    # LLM错误
    ErrorCode.LLM_TIMEOUT: ErrorInfo(
        ErrorCode.LLM_TIMEOUT,
        "LLM请求超时",
        "尝试增加 llm_timeout 配置或减少输入文本长度"
    ),
    ErrorCode.LLM_RATE_LIMIT: ErrorInfo(
        ErrorCode.LLM_RATE_LIMIT,
        "LLM API速率限制",
        "增加 retry_delay 配置或减少并发请求数"
    ),
    ErrorCode.LLM_AUTH_ERROR: ErrorInfo(
        ErrorCode.LLM_AUTH_ERROR,
        "LLM API认证失败",
        "检查 llm_api_key 配置是否正确"
    ),
    ErrorCode.LLM_CONNECTION_ERROR: ErrorInfo(
        ErrorCode.LLM_CONNECTION_ERROR,
        "LLM API连接失败",
        "检查网络连接和 llm_base_url 配置"
    ),
    ErrorCode.LLM_API_ERROR: ErrorInfo(
        ErrorCode.LLM_API_ERROR,
        "LLM API调用失败",
        "检查API参数配置和服务状态"
    ),
    ErrorCode.LLM_EMPTY_RESPONSE: ErrorInfo(
        ErrorCode.LLM_EMPTY_RESPONSE,
        "LLM返回空响应",
        "检查提示词是否清晰，或增加 max_tokens 配置"
    ),
    ErrorCode.LLM_INVALID_RESPONSE: ErrorInfo(
        ErrorCode.LLM_INVALID_RESPONSE,
        "LLM返回无效响应",
        "检查模型配置和提示词格式"
    ),
    
    # 解析错误
    ErrorCode.PARSE_JSON_ERROR: ErrorInfo(
        ErrorCode.PARSE_JSON_ERROR,
        "JSON解析失败",
        "检查LLM返回的原始响应格式，可能需要调整提示词"
    ),
    ErrorCode.PARSE_SCHEMA_ERROR: ErrorInfo(
        ErrorCode.PARSE_SCHEMA_ERROR,
        "Schema校验失败",
        "检查Schema定义是否正确，或LLM返回的数据结构"
    ),
    ErrorCode.PARSE_VALIDATION_ERROR: ErrorInfo(
        ErrorCode.PARSE_VALIDATION_ERROR,
        "数据校验失败",
        "检查数据格式和Schema要求是否匹配"
    ),
    ErrorCode.PARSE_TYPE_MISMATCH: ErrorInfo(
        ErrorCode.PARSE_TYPE_MISMATCH,
        "数据类型不匹配",
        "检查Schema类型定义和LLM返回的数据类型"
    ),
    
    # 数据处理错误
    ErrorCode.DATA_EMPTY_INPUT: ErrorInfo(
        ErrorCode.DATA_EMPTY_INPUT,
        "输入数据为空",
        "检查输入文本是否有效"
    ),
    ErrorCode.DATA_INVALID_FORMAT: ErrorInfo(
        ErrorCode.DATA_INVALID_FORMAT,
        "数据格式无效",
        "检查输入数据的格式和编码"
    ),
    ErrorCode.DATA_MISSING_REQUIRED: ErrorInfo(
        ErrorCode.DATA_MISSING_REQUIRED,
        "缺少必需字段",
        "检查Schema中的required字段定义"
    ),
    ErrorCode.DATA_STRUCTURE_ERROR: ErrorInfo(
        ErrorCode.DATA_STRUCTURE_ERROR,
        "数据结构错误",
        "检查数据的嵌套结构是否正确"
    ),
    
    # 配置错误
    ErrorCode.CONFIG_INVALID_SCHEMA: ErrorInfo(
        ErrorCode.CONFIG_INVALID_SCHEMA,
        "Schema配置无效",
        "检查Schema定义的语法和结构"
    ),
    ErrorCode.CONFIG_MISSING_REQUIRED: ErrorInfo(
        ErrorCode.CONFIG_MISSING_REQUIRED,
        "缺少必需配置",
        "检查配置文件中的必需参数"
    ),
    ErrorCode.CONFIG_INVALID_VALUE: ErrorInfo(
        ErrorCode.CONFIG_INVALID_VALUE,
        "配置值无效",
        "检查配置参数的值和类型"
    ),
    
    # 系统错误
    ErrorCode.SYSTEM_UNKNOWN_ERROR: ErrorInfo(
        ErrorCode.SYSTEM_UNKNOWN_ERROR,
        "未知系统错误",
        "请查看详细日志或联系技术支持"
    ),
    ErrorCode.SYSTEM_RESOURCE_ERROR: ErrorInfo(
        ErrorCode.SYSTEM_RESOURCE_ERROR,
        "系统资源错误",
        "检查系统内存和磁盘空间"
    ),
}


def get_error_info(code: ErrorCode) -> ErrorInfo:
    """获取错误信息
    
    Args:
        code: 错误代码
        
    Returns:
        错误信息对象
    """
    return ERROR_INFO_MAP.get(code, ErrorInfo(
        ErrorCode.SYSTEM_UNKNOWN_ERROR,
        f"未定义的错误代码: {code.value}",
        "请检查错误代码定义"
    ))


def classify_exception(exception: Exception) -> ErrorCode:
    """根据异常类型分类错误代码
    
    Args:
        exception: 异常对象
        
    Returns:
        对应的错误代码
    """
    exception_type = type(exception).__name__
    error_message = str(exception).lower()
    
    # 超时错误
    if "timeout" in exception_type.lower() or "timeout" in error_message:
        return ErrorCode.LLM_TIMEOUT
    
    # 速率限制
    if "rate limit" in error_message or "too many requests" in error_message:
        return ErrorCode.LLM_RATE_LIMIT
    
    # 认证错误
    if ("api key" in error_message or "unauthorized" in error_message or 
        "authentication" in error_message or "401" in error_message):
        return ErrorCode.LLM_AUTH_ERROR
    
    # 连接错误
    if ("connection" in exception_type.lower() or "connection" in error_message or
        "network" in error_message or "dns" in error_message):
        return ErrorCode.LLM_CONNECTION_ERROR
    
    # JSON解析错误
    if "json" in exception_type.lower() or "json" in error_message:
        return ErrorCode.PARSE_JSON_ERROR
    
    # 其他LLM相关错误
    if any(keyword in error_message for keyword in ["api", "openai", "claude", "llm"]):
        return ErrorCode.LLM_API_ERROR
    
    # 默认为未知错误
    return ErrorCode.SYSTEM_UNKNOWN_ERROR
